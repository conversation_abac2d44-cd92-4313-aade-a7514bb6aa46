<?php

namespace app\applent\validate\gift;

use app\common\validate\BaseValidate;

/**
 * 礼物赠送验证器
 * Class GiftSendValidate
 * @package app\applent\validate\gift
 */
class GiftSendValidate extends BaseValidate
{
    protected $rule = [
        'gift_id' => 'require',
        'gift_count' => 'require',
        'source_type' => 'require|in:1,2,3,4',
        'to_user_id' => 'require',
        'type' => 'require|in:0,1,2,3,4',
        'date' => 'requireIf:type,4|dateFormat:Y-m',
    ];

    protected $message = [
        'gift_id.require' => '请选择礼物',
        'gift_count.require' => '请输入礼物数量',
        'source_type.require' => '请指定来源类型',
        'source_type.in' => '来源类型不正确',
        'to_user_id.require' => '请指定接收用户',
        'type.require' => '请选择查询类型',
        'type.in' => '查询类型不正确',
        'date.requireIf' => '自定义查询时请选择日期',
        'date.dateFormat' => '日期格式不正确，请使用年-月格式',
    ];

    /**
     * 赠送礼物场景
     */
    public function sceneSendGift()
    {
        return $this->only(['gift_id', 'gift_count', 'source_type', 'to_user_id']);
    }

    /**
     * 我的收礼记录场景
     */
    public function sceneMyGiftRecords()
    {
        return $this->only(['type', 'date']);
    }
}
