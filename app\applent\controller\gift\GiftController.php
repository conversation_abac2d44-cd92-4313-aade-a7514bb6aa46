<?php

namespace app\applent\controller\gift;

use app\applent\controller\BaseApiController;
use app\applent\logic\gift\GiftLogic;
use app\applent\validate\gift\GiftSendValidate;
use app\common\service\JsonService;
use app\applent\lists\gift\GiftLists;
/**
 * 礼物控制器
 * Class GiftController
 * @package app\applent\controller\gift
 */
class GiftController extends BaseApiController
{

    /**
     * @notes 获取礼物列表
     * @return \think\response\Json
     */
    public function gift_list()
    {
        return $this->dataLists(new GiftLists());
    }

    /**
     * @notes 赠送礼物
     * @return \think\response\Json
     */
    public function send_gift()
    {
        $params = (new GiftSendValidate())->post()->goCheck('sendGift');
        $params['user_id'] = $this->userId;
        $params['nickname'] = $this->userInfo['nickname'];
        $params['avatar'] = $this->userInfo['avatar'];
        $result = GiftLogic::sendGift($params);
        if ($result === false) {
            return JsonService::fail(GiftLogic::getError());
        }
        return JsonService::success('礼物赠送成功', $result, 1, 0);
    }
}