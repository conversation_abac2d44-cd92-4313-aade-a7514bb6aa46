<?php

namespace app\applent\controller\gift;

use app\applent\controller\BaseApiController;
use app\applent\logic\gift\GiftLogic;
use app\applent\validate\gift\GiftSendValidate;
use app\common\model\gift\Gift;
use app\common\service\JsonService;
use app\common\service\ConfigService;
use think\facade\Cache;

/**
 * 礼物控制器
 * Class GiftController
 * @package app\applent\controller\gift
 */
class GiftController extends BaseApiController
{

    /**
     * @notes 获取礼物列表
     * @return \think\response\Json
     */
    public function gift_list()
    {
        try {
            // 获取货币单位配置
            $currencyName = ConfigService::get('systemconfig', 'currency_name', '金币');

            // 查询礼物列表，只获取需要的字段
            $giftList = Gift::field('id,name,img,coin')
                ->where('status', 1) // 只获取启用的礼物
                ->order('sort desc, id asc') // 按排序和ID排序
                ->select()
                ->toArray();

            // 处理数据格式
            foreach ($giftList as &$gift) {
                $gift['gift_id'] = $gift['id'];
                $gift['gift_name'] = $gift['name'];
                $gift['gift_icon'] = $gift['img']; // img字段会自动通过获取器处理为完整URL
                $gift['gift_price'] = $gift['coin'] . $currencyName;

                // 移除不需要的字段
                unset($gift['id'], $gift['name'], $gift['img'], $gift['coin']);
            }

            return JsonService::success('获取成功', $giftList);

        } catch (\Exception $e) {
            return JsonService::fail('获取礼物列表失败：' . $e->getMessage());
        }
    }

    /**
     * @notes 赠送礼物
     * @return \think\response\Json
     */
    public function send_gift()
    {
        $params = (new GiftSendValidate())->post()->goCheck('sendGift');
        $params['user_id'] = $this->userId;
        $params['nickname'] = $this->userInfo['nickname'];
        $params['avatar'] = $this->userInfo['avatar'];
        $result = GiftLogic::sendGift($params);
        if ($result === false) {
            return JsonService::fail(GiftLogic::getError());
        }
        return JsonService::success('礼物赠送成功', $result, 1, 0);
    }
}