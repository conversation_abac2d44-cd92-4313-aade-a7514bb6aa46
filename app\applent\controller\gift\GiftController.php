<?php

namespace app\applent\controller\gift;

use app\applent\controller\BaseApiController;
use app\applent\logic\gift\GiftLogic;
use app\applent\validate\gift\GiftSendValidate;
use app\common\model\gift\Gift;
use app\common\service\JsonService;
use think\facade\Cache;

/**
 * 礼物控制器
 * Class GiftController
 * @package app\applent\controller\gift
 */
class GiftController extends BaseApiController
{

    /**
     * @notes 赠送礼物
     * @return \think\response\Json
     */
    public function send_gift()
    {
        $params = (new GiftSendValidate())->post()->goCheck('sendGift');
        $params['user_id'] = $this->userId;
        $result = GiftLogic::sendGift($params);
        if ($result === false) {
            return JsonService::fail(GiftLogic::getError());
        }
        return JsonService::success('礼物赠送成功', $result);
    }
}