<?php
namespace app\common\model\gift;

use app\common\model\BaseModel;
use app\common\service\FileService;
use app\common\service\ConfigService;

/**
 * 礼物模型
 * Class Gift
 * @package app\common\model\gift
 */
class Gift extends BaseModel
{
    protected $name = 'gift';

    /**
     * 获取礼物图片完整URL
     * @param $value
     * @return string
     */
    public function getImgAttr($value)
    {
        return trim($value) ? FileService::getFileUrl($value) : '';
    }

    /**
     * @notes 礼物金额获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getCoinTextAttr($value, $data)
    {
        $coin = $data['coin'] ?? $value;
        return $coin . ConfigService::get('systemconfig', 'currency_name', '金币');
    }

    public function getAddtimeAttr($value)
    {
        return empty($value) ? '' : date('Y-m-d H:i:s', $value);
    }

    /**
     * @notes 礼物类型获取器
     * @param $value
     * @return string
     */
    public function getTypeDescAttr($value, $data)
    {
        $typeMap = [
            1 => '普通',
            2 => '守护',
            3 => '动画svge'
        ];
        return $typeMap[$data['type']] ?? '未知类型';
    }

    /**
     * @notes 礼物分类获取器
     * @param $value
     * @return string
     */
    public function getGiftTypeDescAttr($value, $data)
    {
        $giftTypeMap = [
            1 => '普通',
            2 => '连续'
        ];
        return $giftTypeMap[$data['gift_type']] ?? '未知分类';
    }

    /**
     * @notes 是否全局推送获取器 is_all_notify
     * @param $value
     * @return string
     */
    public function getIsAllNotifyDescAttr($value, $data)
    {
        $isAllNotifyMap = [
            0 => '否',
            1 => '是'
        ];
        return $isAllNotifyMap[$data['is_all_notify']];
    }

    /**
     * @notes 是否搭讪礼物获取器 is_accost
     * @param $value
     * @return string
     */
    public function getIsAccostDescAttr($value, $data)
    {
        $isAccostMap = [
            0 => '否',
            1 => '是'
        ];
        return $isAccostMap[$data['is_accost']];
    }
}
