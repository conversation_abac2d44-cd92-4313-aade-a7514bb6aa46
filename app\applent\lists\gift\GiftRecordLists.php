<?php

namespace app\applent\lists\gift;

use app\applent\lists\BaseApiDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\gift\GiftRecord;
use app\common\service\ConfigService;

/**
 * 收礼记录列表
 * Class GiftRecordLists
 * @package app\applent\lists\gift
 */
class GiftRecordLists extends BaseApiDataLists implements ListsSearchInterface
{
    protected $recordFields = 'id,user_id,nickname,gift_name,gift_img,gift_coin,actual_income,gift_count,create_time';
    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
        $searchFields = ['type', 'date'];
        //获取两个数组交集
        return array_intersect(array_keys($this->params), $searchFields);
    }


    /**
     * @notes 设置排序
     * @return array
     */
    public function setOrder(): array
    {
        return ['create_time' => 'desc'];
    }

    /**
     * @notes 获取列表数据
     * @return array
     */
    public function lists(): array
    {
        $lists = GiftRecord::where(['to_user_id' => $this->userId])
            ->field($this->recordFields)
            ->withSearch($this->setSearch(), $this->params)
            ->order('create_time desc')
            ->page($this->pageNo, $this->pageSize)
            ->append(['gift_coin_text', 'actual_income_text'])
            ->select()
            ->toArray();
        return $lists;
    }


    /**
     * @notes 获取数量
     * @return int
     */
    public function count(): int
    {
        return GiftRecord::where(['to_user_id' => $this->userId])->count();
    }

}
