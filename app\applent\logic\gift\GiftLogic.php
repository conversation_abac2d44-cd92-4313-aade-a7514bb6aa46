<?php

namespace app\applent\logic\gift;

use app\common\logic\BaseLogic;
use app\common\model\gift\Gift;
use app\common\model\user\User;
use app\common\model\user\UserBalance;
use app\common\model\user\UserCoinLog;
use app\common\model\user\UserIncomeLog;
use app\common\service\ConfigService;
use think\facade\Db;
use think\facade\Log;

/**
 * 礼物业务逻辑类
 */
class GiftLogic extends BaseLogic
{
    /**
     * @notes 赠送礼物
     * @param array $params 参数
     * @return array|bool
     */
    public static function sendGift($params)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 2. 基础验证
            if ($params['user_id'] == $params['to_user_id']) {
                throw new \Exception('不能给自己赠送礼物');
            }

            // 3. 获取礼物信息
            $gift = Gift::find($params['gift_id']);
            if (!$gift) {
                throw new \Exception('礼物不存在');
            }

            // 4. 获取接收用户信息
            $toUser = User::find($params['to_user_id']);
            if (!$toUser) {
                throw new \Exception('接收用户不存在');
            }

            // 5. 获取赠送用户信息
            $fromUser = User::find($params['user_id']);
            if (!$fromUser) {
                throw new \Exception('用户不存在');
            }

            // 6. 计算总金额
            $totalAmount = $gift->coin * $params['gift_count'];

            // 7. 生成订单号
            $orderNo = 'GIFT_' . date('YmdHis') . mt_rand(1000, 9999);

            // 8. 获取赠送用户当前余额
            $senderBalance = UserBalance::getUserBalance($params['user_id']);
            $beforeSenderBalance = $senderBalance ? $senderBalance['balance'] : 0;

            // 9. 检查用户余额
            if ($beforeSenderBalance < $totalAmount) {
                throw new \Exception('余额不足，请先充值');
            }

            // 10. 扣除赠送用户余额
            $deductResult = UserBalance::subBalance($params['user_id'], $totalAmount);
            if (!$deductResult) {
                throw new \Exception('余额扣除失败');
            }

            // 11. 记录赠送用户余额变动日志
            $afterSenderBalance = $beforeSenderBalance - $totalAmount;
            $senderLogResult = UserCoinLog::addGiftLog(
                $params['user_id'],
                $beforeSenderBalance,
                $afterSenderBalance,
                -$totalAmount,
                $orderNo,
                $params['to_user_id']
            );

            if (!$senderLogResult) {
                throw new \Exception('赠送用户余额变动日志记录失败');
            }

            // 12. 计算接收用户收益（根据用户设置或系统配置计算抽成）
            $commissionRate = self::calculateCommissionRate($params['to_user_id']);
            $actualIncome = $totalAmount * (100 - $commissionRate) / 100;

            // 13. 获取接收用户当前收益
            $receiverBalance = UserBalance::getUserBalance($params['to_user_id']);
            $beforeReceiverIncome = $receiverBalance ? $receiverBalance['income'] : 0;

            // 14. 增加接收用户收益
            $addIncomeResult = UserBalance::addIncome($params['to_user_id'], $actualIncome);
            if (!$addIncomeResult) {
                throw new \Exception('接收用户收益增加失败');
            }

            // 15. 记录接收用户收益变动日志
            $afterReceiverIncome = $beforeReceiverIncome + $actualIncome;
            $receiverLogResult = UserIncomeLog::addGiftReceiveLog(
                $params['to_user_id'],
                $beforeReceiverIncome,
                $afterReceiverIncome,
                $actualIncome,
                $orderNo,
                $params['user_id'],
                $gift->name,
                $params['gift_count'],
                $commissionRate,
                $totalAmount
            );

            if (!$receiverLogResult) {
                throw new \Exception('接收用户收益变动日志记录失败');
            }

            // 16. 创建礼物记录
            $giftLogData = [
                'user_id' => $params['user_id'],
                'to_user_id' => $params['to_user_id'],
                'gift_id' => $params['gift_id'],
                'gift_name' => $gift->name,
                'gift_count' => $params['gift_count'],
                'gift_coin' => $totalAmount,
                'profit' => $actualIncome,
                'source_type' => $params['source_type'] ?? 1,
                'source_id' => $params['source_id'] ?? '',
                'order_no' => $orderNo,
                'create_time' => time(),
            ];

            $giftLogId = Db::name('user_gift_log')->insertGetId($giftLogData);

            // 17. 处理返佣（如果开启）
            $commissionInfo = calculate_user_commission($params['to_user_id'], $totalAmount, 2); // 类型2=礼物
            if (!empty($commissionInfo)) {
                self::handleCommissionReward($commissionInfo, $orderNo, $params['user_id']);
            }

            // 18. 全局广播（如果需要）
            // if ($gift->is_all_notify == 1) {
            //     self::sendGlobalGiftMessage($fromUser, $toUser, $gift, $params['gift_count']);
            // }

            // 提交事务
            Db::commit();

            return [
                'order_no' => $orderNo,
                'gift_name' => $gift->name,
                'gift_count' => $params['gift_count'],
                'total_amount' => $totalAmount,
                'actual_income' => $actualIncome,
                'remaining_balance' => $afterSenderBalance,
                'is_global' => $gift->is_all_notify ?? 0
            ];

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 处理返佣奖励
     * @param array $commissionInfo 返佣信息
     * @param string $orderNo 订单号
     * @param int $fromUserId 赠送用户ID
     * @return bool
     * @throws \Exception
     */
    private static function handleCommissionReward($commissionInfo, $orderNo, $fromUserId)
    {
        try {
            foreach ($commissionInfo as $level => $info) {
                if (empty($info['user_id']) || empty($info['commission'])) {
                    continue;
                }

                $inviterId = $info['user_id'];
                $commissionAmount = $info['commission'];
                $commissionRate = $info['rate'];
                $levelNum = $level == 'level1' ? 1 : 2;

                // 获取邀请人当前收益
                $inviterBalance = UserBalance::getUserBalance($inviterId);
                $beforeIncome = $inviterBalance ? $inviterBalance['income'] : 0;

                // 增加邀请人收益
                $addIncomeResult = UserBalance::addIncome($inviterId, $commissionAmount);
                if (!$addIncomeResult) {
                    throw new \Exception('邀请人收益增加失败');
                }

                // 记录收益变动日志
                $afterIncome = $beforeIncome + $commissionAmount;
                $incomeLogResult = UserIncomeLog::addGiftCommissionLog(
                    $inviterId,
                    $beforeIncome,
                    $afterIncome,
                    $commissionAmount,
                    $levelNum,
                    $orderNo,
                    $fromUserId,
                    $commissionRate,
                    0 // 礼物返佣不需要记录原始金额
                );

                if (!$incomeLogResult) {
                    throw new \Exception('收益变动日志记录失败');
                }
            }

            return true;
        } catch (\Exception $e) {
            // 返佣失败不影响主流程，只记录日志
            Log::error('礼物返佣处理失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 计算用户礼物抽成比例
     * @param int $userId 用户ID
     * @return float 抽成比例
     */
    private static function calculateCommissionRate($userId)
    {
        // 获取用户信息
        $user = User::find($userId);
        if (!$user) {
            // 用户不存在，使用系统默认配置
            return floatval(ConfigService::get('systemconfig', 'gift_commission', 50));
        }

        // 检查用户是否开启个人抽成设置
        if ($user->is_open_gift_commission == 1) {
            // 从用户返佣比例表获取
            $userRebate = \app\common\model\user\UserRebateRatio::where('user_id', $userId)->find();
            if ($userRebate && isset($userRebate->gift_commission)) {
                return floatval($userRebate->gift_commission);
            }
        }

        // 使用系统默认配置
        return floatval(ConfigService::get('systemconfig', 'gift_commission', 50));
    }
}
