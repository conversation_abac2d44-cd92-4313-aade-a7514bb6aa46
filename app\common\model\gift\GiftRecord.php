<?php

namespace app\common\model\gift;

use app\common\model\BaseModel;
use app\common\model\user\User;
use app\common\service\ConfigService;
/**
 * 礼物记录模型
 * Class GiftRecord
 * @package app\common\model\gift
 */
class GiftRecord extends BaseModel
{
    protected $name = 'user_gift_records';

    // 来源类型常量
    const SOURCE_PROFILE = 1;    // 查看个人资料
    const SOURCE_VIDEO_CALL = 2; // 视频通话
    const SOURCE_MESSAGE = 3;    // 消息页面
    const SOURCE_LIVE_ROOM = 4;  // 直播间

    /**
     * @notes 关联送礼用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * @notes 关联接收礼物用户
     * @return \think\model\relation\BelongsTo
     */
    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_id', 'id');
    }

    /**
     * @notes 关联礼物
     * @return \think\model\relation\BelongsTo
     */
    public function gift()
    {
        return $this->belongsTo(Gift::class, 'gift_id', 'id');
    }

    /**
     * @notes 来源类型获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getSourceTypeDescAttr($value, $data)
    {
        $sourceMap = [
            self::SOURCE_PROFILE => '查看个人资料',
            self::SOURCE_VIDEO_CALL => '视频通话',
            self::SOURCE_MESSAGE => '消息页面',
            self::SOURCE_LIVE_ROOM => '直播间'
        ];
        return $sourceMap[$data['source_type']] ?? '未知来源';
    }

    /**
     * @notes 是否全局广播获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getIsGlobalDescAttr($value, $data)
    {
        return $data['is_global'] ? '是' : '否';
    }

    /**
     * @notes 创建时间获取器
     * @param $value
     * @return string
     */
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * @notes 礼物金额获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getGiftCoinTextAttr($value, $data)
    {
        return $data['gift_coin'] . ConfigService::get('systemconfig', 'currency_name', '金币');
    }
    /**
     * @notes 实际收益获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getActualIncomeTextAttr($value, $data)
    {
        return $data['actual_income'] . ConfigService::get('systemconfig', 'profit_name', '钻石');
    }

    /**
     * @notes 添加礼物记录
     * @param array $data 礼物记录数据
     * @return GiftRecord|false
     */
    public static function addRecord($data)
    {
        try {
            return self::create([
                'user_id' => $data['user_id'],
                'to_user_id' => $data['to_user_id'],
                'to_nickname' => $data['to_nickname'],
                'gift_id' => $data['gift_id'],
                'gift_name' => $data['gift_name'],
                'gift_count' => $data['gift_count'],
                'gift_price' => $data['gift_price'],
                'total_amount' => $data['total_amount'],
                'commission_rate' => $data['commission_rate'],
                'actual_income' => $data['actual_income'],
                'source_type' => $data['source_type'],
                'source_id' => $data['source_id'] ?? '',
                'is_global' => $data['is_global'] ?? 0,
                'create_time' => time(),
                'update_time' => time(),
            ]);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @notes 获取用户送出的礼物记录
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getUserSentGifts($userId, $page = 1, $limit = 20)
    {
        return self::where('user_id', $userId)
            ->with(['toUser', 'gift'])
            ->order('create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
    }

    /**
     * @notes 获取用户收到的礼物记录
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getUserReceivedGifts($userId, $page = 1, $limit = 20)
    {
        return self::where('to_user_id', $userId)
            ->with(['user', 'gift'])
            ->order('create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
    }

    /**
     * @notes 统计用户送出礼物总金额
     * @param int $userId 用户ID
     * @return float
     */
    public static function getUserSentTotal($userId)
    {
        return self::where('user_id', $userId)->sum('total_amount') ?: 0;
    }

    /**
     * @notes 统计用户收到礼物总收益
     * @param int $userId 用户ID
     * @return float
     */
    public static function getUserReceivedTotal($userId)
    {
        return self::where('to_user_id', $userId)->sum('actual_income') ?: 0;
    }
}
