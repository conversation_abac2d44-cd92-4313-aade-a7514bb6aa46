<?php

namespace app\common\model\user;

use app\common\model\BaseModel;

/**
 * 用户收益变动记录模型
 * Class UserIncomeLog
 * @package app\common\model\user
 */
class UserIncomeLog extends BaseModel
{
    protected $name = 'user_income_log';

    /**
     * @notes 关联用户模型
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * @notes 关联来源用户模型
     * @return \think\model\relation\BelongsTo
     */
    public function fromUser()
    {
        return $this->belongsTo(User::class, 'to_user_id', 'id');
    }

    /**
     * @notes 收益来源文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getSourceTextAttr($value, $data)
    {
        $source = $data['source'] ?? 0;
        $sourceTexts = [
            1 => '礼物收益',    // 礼物收益
            2 => '语音视频收益', // 语音视频收益
            3 => '充值收益',    // 充值收益一级返佣
            4 => '充值收益',    // 充值收益二级返佣
            5 => '礼物收益',    // 礼物收益一级返佣
            6 => '礼物收益',    // 礼物收益二级返佣
            7 => '通话收益',    // 语音视频通话一级返佣
            8 => '通话收益',    // 语音视频通话二级返佣
            9 => '申请提现',    // 申请提现
            10 => '提现驳回',   // 提现驳回
            11 => '首冲奖励',   // 首冲额外奖励
            12 => '会员收益',   // 会员充值收益一级返佣
            13 => '会员收益',   // 会员充值收益二级返佣
        ];

        return $sourceTexts[$source] ?? '其他收益';
    }

    // 变动类型常量
    const TYPE_ADD = 1; // 增加
    const TYPE_SUB = 2; // 减少

    // 收益来源常量
    const SOURCE_GIFT_INCOME = 1;           // 礼物收益
    const SOURCE_CALL_INCOME = 2;           // 语音视频收益
    const SOURCE_RECHARGE_LEVEL1 = 3;       // 充值收益一级返佣
    const SOURCE_RECHARGE_LEVEL2 = 4;       // 充值收益二级返佣
    const SOURCE_GIFT_LEVEL1 = 5;           // 礼物收益一级返佣
    const SOURCE_GIFT_LEVEL2 = 6;           // 礼物收益二级返佣
    const SOURCE_CALL_LEVEL1 = 7;           // 语音视频通话一级返佣
    const SOURCE_CALL_LEVEL2 = 8;           // 语音视频通话二级返佣
    const SOURCE_WITHDRAW_APPLY = 9;        // 申请提现
    const SOURCE_WITHDRAW_REJECT = 10;      // 提现驳回
    const SOURCE_FIRST_CHARGE_REWARD = 11;  // 首冲额外奖励

    /**
     * @notes 记录收益变动日志
     * @param int $userId 用户ID
     * @param float $beforeIncome 变动前收益金额
     * @param float $afterIncome 变动后收益金额
     * @param float $changeIncome 变动收益金额（正数增加，负数减少）
     * @param int $source 来源
     * @param int $type 类型：1=增加,2=减少
     * @param string $sourceNo 来源单号
     * @param int $toUserId 来源用户ID
     * @param string $toNickname 来源用户昵称
     * @param float $commissionRate 佣金比例（可选）
     * @param float $totalIncome 抽成前金额（可选）
     * @param string $giftName 礼物名称（可选）
     * @param int $giftNum 礼物数量（可选）
     * @param float $giftPrice 礼物单价（可选）
     * @return bool
     */
    public static function addLog($userId, $beforeIncome, $afterIncome, $changeIncome, $source, $type, $sourceNo, $toUserId, $toNickname = '', $commissionRate = 0, $totalIncome = 0, $giftName = '', $giftNum = 0, $giftPrice = 0)
    {
        // 如果没有传递昵称且有来源用户ID，自动获取昵称
        if (empty($toNickname) && $toUserId > 0) {
            $toUser = User::where('id', $toUserId)->find();
            $toNickname = $toUser ? $toUser->nickname : '';
        }

        $data = [
            'user_id' => $userId,
            'before_income' => $beforeIncome,
            'after_income' => $afterIncome,
            'change_income' => $changeIncome,
            'source' => $source,
            'type' => $type,
            'source_no' => $sourceNo,
            'to_user_id' => $toUserId,
            'to_nickname' => $toNickname,
            'commission_rate' => $commissionRate,
            'total_income' => $totalIncome,
            'gift_name' => $giftName,
            'gift_num' => $giftNum,
            'gift_price' => $giftPrice,
            'create_time' => time(),
            'update_time' => time(),
        ];

        return self::create($data) ? true : false;
    }

    /**
     * @notes 记录充值返佣收益日志
     * @param int $userId 用户ID
     * @param float $beforeIncome 变动前收益
     * @param float $afterIncome 变动后收益
     * @param float $commissionAmount 返佣金额
     * @param int $level 返佣级别：1=一级,2=二级
     * @param string $orderNo 充值订单号
     * @param int $fromUserId 来源用户ID（充值的用户ID）
     * @param float $commissionRate 佣金比例
     * @param float $totalIncome 抽成前金额（下级用户充值金额*10）
     * @return bool
     */
    public static function addRechargeCommissionLog($userId, $beforeIncome, $afterIncome, $commissionAmount, $level, $orderNo, $fromUserId, $commissionRate = 0, $totalIncome = 0)
    {
        $source = $level == 1 ? self::SOURCE_RECHARGE_LEVEL1 : self::SOURCE_RECHARGE_LEVEL2;

        return self::addLog(
            $userId,
            $beforeIncome,
            $afterIncome,
            $commissionAmount,
            $source,
            self::TYPE_ADD,
            $orderNo,
            $fromUserId,
            '', // toNickname 自动获取
            $commissionRate,
            $totalIncome
        );
    }

    /**
     * @notes 记录礼物返佣收益日志
     * @param int $userId 用户ID
     * @param float $beforeIncome 变动前收益
     * @param float $afterIncome 变动后收益
     * @param float $commissionAmount 返佣金额
     * @param int $level 返佣级别：1=一级,2=二级
     * @param string $orderNo 礼物订单号
     * @param int $fromUserId 来源用户ID（送礼的用户ID）
     * @param float $commissionRate 佣金比例
     * @param float $totalIncome 抽成前金额（礼物总价值*10）
     * @return bool
     */
    public static function addGiftCommissionLog($userId, $beforeIncome, $afterIncome, $commissionAmount, $level, $orderNo, $fromUserId, $commissionRate = 0, $totalIncome = 0)
    {
        $source = $level == 1 ? self::SOURCE_GIFT_LEVEL1 : self::SOURCE_GIFT_LEVEL2;

        return self::addLog(
            $userId,
            $beforeIncome,
            $afterIncome,
            $commissionAmount,
            $source,
            self::TYPE_ADD,
            $orderNo,
            $fromUserId,
            '', // toNickname 自动获取
            $commissionRate,
            $totalIncome
        );
    }

    /**
     * @notes 记录语音视频通话返佣收益日志
     * @param int $userId 用户ID
     * @param float $beforeIncome 变动前收益
     * @param float $afterIncome 变动后收益
     * @param float $commissionAmount 返佣金额
     * @param int $level 返佣级别：1=一级,2=二级
     * @param string $orderNo 通话订单号
     * @param int $fromUserId 来源用户ID（通话发起者用户ID）
     * @param float $commissionRate 佣金比例
     * @param float $totalIncome 抽成前金额（通话总费用*10）
     * @return bool
     */
    public static function addCallCommissionLog($userId, $beforeIncome, $afterIncome, $commissionAmount, $level, $orderNo, $fromUserId, $commissionRate = 0, $totalIncome = 0)
    {
        $source = $level == 1 ? self::SOURCE_CALL_LEVEL1 : self::SOURCE_CALL_LEVEL2;

        return self::addLog(
            $userId,
            $beforeIncome,
            $afterIncome,
            $commissionAmount,
            $source,
            self::TYPE_ADD,
            $orderNo,
            $fromUserId,
            '', // toNickname 自动获取
            $commissionRate,
            $totalIncome
        );
    }

    /**
     * @notes 记录提现申请日志
     * @param int $userId 用户ID
     * @param float $beforeIncome 变动前收益
     * @param float $afterIncome 变动后收益
     * @param float $withdrawAmount 提现金额
     * @param string $withdrawNo 提现申请单号
     * @return bool
     */
    public static function addWithdrawApplyLog($userId, $beforeIncome, $afterIncome, $withdrawAmount, $withdrawNo)
    {
        return self::addLog(
            $userId,
            $beforeIncome,
            $afterIncome,
            -$withdrawAmount,
            self::SOURCE_WITHDRAW_APPLY,
            self::TYPE_SUB,
            $withdrawNo,
            0, // 提现时to_user_id为0
            '' // 提现时to_nickname为空
        );
    }

    /**
     * @notes 记录礼物接收收益日志
     * @param int $userId 用户ID
     * @param float $beforeIncome 变动前收益
     * @param float $afterIncome 变动后收益
     * @param float $incomeAmount 收益金额
     * @param string $orderNo 礼物订单号
     * @param int $fromUserId 赠送用户ID
     * @param string $giftName 礼物名称
     * @param int $giftCount 礼物数量
     * @param float $commissionRate 抽成比例
     * @param float $totalAmount 礼物总价值
     * @param float $giftPrice 礼物单价
     * @return bool
     */
    public static function addGiftReceiveLog($userId, $beforeIncome, $afterIncome, $incomeAmount, $orderNo, $fromUserId, $giftName = '', $giftCount = 1, $commissionRate = 0, $totalAmount = 0, $giftPrice = 0)
    {
        var_dump($giftPrice);die;
        return self::addLog(
            $userId,
            $beforeIncome,
            $afterIncome,
            $incomeAmount,
            self::SOURCE_GIFT_INCOME,
            self::TYPE_ADD,
            $orderNo,
            $fromUserId,
            '', // toNickname 自动获取
            $commissionRate,
            $totalAmount,
            $giftName,
            $giftCount,
            $giftPrice
        );
    }

    /**
     * @notes 记录提现驳回日志
     * @param int $userId 用户ID
     * @param float $beforeIncome 变动前收益
     * @param float $afterIncome 变动后收益
     * @param float $withdrawAmount 提现金额
     * @param string $withdrawNo 提现申请单号
     * @return bool
     */
    public static function addWithdrawRejectLog($userId, $beforeIncome, $afterIncome, $withdrawAmount, $withdrawNo)
    {
        return self::addLog(
            $userId,
            $beforeIncome,
            $afterIncome,
            $withdrawAmount,
            self::SOURCE_WITHDRAW_REJECT,
            self::TYPE_ADD,
            $withdrawNo,
            0, // 提现驳回时to_user_id为0
            '' // 提现驳回时to_nickname为空
        );
    }

    /**
     * @notes 记录首冲额外奖励日志
     * @param int $userId 用户ID（上级用户ID）
     * @param float $beforeIncome 变动前收益
     * @param float $afterIncome 变动后收益
     * @param float $rewardAmount 奖励金额
     * @param string $orderNo 充值订单号
     * @param int $fromUserId 来源用户ID（首冲用户ID）
     * @return bool
     */
    public static function addFirstChargeRewardLog($userId, $beforeIncome, $afterIncome, $rewardAmount, $orderNo, $fromUserId)
    {
        return self::addLog(
            $userId,
            $beforeIncome,
            $afterIncome,
            $rewardAmount,
            self::SOURCE_FIRST_CHARGE_REWARD,
            self::TYPE_ADD,
            $orderNo,
            $fromUserId
        );
    }

    /**
     * @notes 获取用户收益变动日志
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param int $source 来源筛选（可选）
     * @return array
     */
    public static function getUserIncomeLogs($userId, $page = 1, $limit = 20, $source = 0)
    {
        $where = ['user_id' => $userId];
        if ($source > 0) {
            $where['source'] = $source;
        }

        $list = self::where($where)
            ->field('type,change_income,before_income,after_income,source,source_no,to_user_id,create_time')
            ->order('create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();

        $total = self::where($where)->count();

        // 处理数据
        foreach ($list as &$item) {
            $item['type_text'] = $item['type'] == self::TYPE_ADD ? '增加' : '减少';
            $item['source_text'] = self::getSourceText($item['source']);
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
        }

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit),
        ];
    }

    /**
     * @notes 获取来源文本
     * @param int $source 来源
     * @return string
     */
    private static function getSourceText($source)
    {
        $sourceTexts = [
            self::SOURCE_GIFT_INCOME => '礼物收益',
            self::SOURCE_CALL_INCOME => '语音视频收益',
            self::SOURCE_RECHARGE_LEVEL1 => '充值收益一级返佣',
            self::SOURCE_RECHARGE_LEVEL2 => '充值收益二级返佣',
            self::SOURCE_GIFT_LEVEL1 => '礼物收益一级返佣',
            self::SOURCE_GIFT_LEVEL2 => '礼物收益二级返佣',
            self::SOURCE_CALL_LEVEL1 => '语音视频通话一级返佣',
            self::SOURCE_CALL_LEVEL2 => '语音视频通话二级返佣',
            self::SOURCE_WITHDRAW_APPLY => '申请提现',
            self::SOURCE_WITHDRAW_REJECT => '提现驳回',
            self::SOURCE_FIRST_CHARGE_REWARD => '首冲额外奖励',
        ];

        return $sourceTexts[$source] ?? '未知';
    }

    /**
     * @notes 按月份搜索器
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchDateAttr($query, $value, $data)
    {
        if (!empty($value)) {
            // 解析 YYYY-MM 格式
            $parts = explode('-', $value);
            if (count($parts) == 2) {
                $year = (int)$parts[0];
                $month = (int)$parts[1];

                // 计算该月的开始和结束时间
                $startTime = strtotime($year . '-' . sprintf('%02d', $month) . '-01 00:00:00');
                $endTime = strtotime(date('Y-m-t 23:59:59', $startTime)); // 该月最后一天

                $query->where('create_time', 'between', [$startTime, $endTime]);
            }
        } else {
            // 如果没有传入日期，默认查找最近一周的数据
            $startTime = strtotime('-7 days');
            $endTime = time();
            $query->where('create_time', 'between', [$startTime, $endTime]);
        }
    }
}
