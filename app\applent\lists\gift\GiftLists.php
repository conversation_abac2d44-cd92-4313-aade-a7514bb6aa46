<?php

namespace app\applent\lists\gift;

use app\applent\lists\BaseApiDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\gift\Gift;
/**
 * 我的邀请列表
 * Class GiftLists
 * @package app\applent\lists\invite
 */
class GiftLists extends BaseApiDataLists implements ListsSearchInterface
{
    protected $inviteFields = 'id,name,img,coin';

    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
        return [];
    }

    /**
     * @notes 获取我的邀请列表
     * @return array
     */
    public function lists(): array
    {
        $invites = Gift::field($this->inviteFields)
            ->order('orderno desc, id asc')
            ->page($this->pageNo, $this->pageSize)
            ->append(['coin_text'])
            ->select()
            ->toArray();

        return $invites;
    }

    /**
     * @notes 获取我的邀请列表总数
     * @return int
     */
    public function count(): int
    {
        return Gift::count();
    }
}
